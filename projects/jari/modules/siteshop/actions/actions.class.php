<?php

  use domain\productrange\collection\ProductContainers;
  use domain\productrange\service\GetCategories;
  use domain\productrange\service\GetCategory;
  use domain\productrange\service\GetProductContainer;
  use domain\productrange\service\GetProductContainers;
  use domain\productrange\service\GetProducts;

  class siteshopJariActions extends siteshopActions {

    public function preExecute() {
      parent::preExecute();

      $this->showBreadcrumbs = count(BreadCrumbs::getInstance()->getItems()) > 1;
    }

    public function executeGetCategoriesAjax(): void {
      $pager = new Pager();
      $pager->setWriteCount(true);
      $pager->setWriteRowsPerPageOptions(true, 12);
      $pager->handle();

      $getter = new GetCategories('nl');

      // If categoryId is set, get subcategories of that category, otherwise get all categories
      $categories = isset($_GET['categoryId']) ?
        $getter->getSubCategoriesOf($_GET['categoryId'], $pager) :
        $getter->getAllOnline();

      array_walk($categories, function ($product) {
        $this->setImageUrl($product);
      });

      ResponseHelper::exitAsJson([
        'categories' => $categories,
        'pager' => $pager
      ]);
    }

    public function executeGetProductContainersAjax(): void {
      $pager = new Pager();
      $pager->setWriteCount(true);
      $pager->setWriteRowsPerPageOptions(true, 12);
      $pager->handle();

      $categoryId = $_GET['categoryId'] ?? null;
      $filters = $_GET['filters'] ?? [];

      $getter = new GetProductContainers();
      $products = $getter->getOnlineOfCategory($categoryId, $pager, $filters);
      array_walk($products, function ($product) {
        $this->setImageUrl($product);
      });
      $options = $getter->getGroupedOptionsFromProductContainers($products);

      ResponseHelper::exitAsJson([
        'products' => $products,
        'pager' => $pager,
        'options' => $options,
      ]);
    }

    public function executeGetProductsAjax(): void {
      $containerId = $_GET['containerId'];
      $container = ProductCont::find_by_id($containerId);

      $products = new GetProducts()->getOnline($container);
      ResponseHelper::exitAsJson($products);
    }

    public function executeHome() {
      $page = Page::getPageAndContent($this->pageId, $_SESSION['lang']);
      $this->page = $page;

      $categories = (new GetCategories('nl'))->getAllOnline();
      array_walk($categories, function ($category) {
        $this->setImageUrl($category);
      });
      $this->categories = $categories;
    }

    public function executeCategory() {
      Context::addJavascriptInlineBottomFile(DIR_PROJECT_FOLDER . 'modules/site/js/parralax.js');

      if (!isset($_GET['var2']) || (int)$_GET['var2'] == 0) {
        MessageFlashCoordinator::addMessageAlert('Helaas... Deze categorie is niet meer beschikbaar. U bent doorgestuurd naar de homepage.');
        ResponseHelper::redirect301('/');
      }

      $category_id = (int)$_GET['var2'];

      $getCategory = new GetCategory($category_id);
      try {
        $category = $getCategory->getOnline();
      }
      catch (Exception $exception) {
        logToFile('executeCategory', print_r($exception, true));

        MessageFlashCoordinator::addMessageAlert('Helaas... Deze categorie is niet meer beschikbaar. U bent doorgestuurd naar de homepage.');
        ResponseHelper::redirect301('/');
      }

      $pager = new Pager();
      $pager->setWriteCount(true);
      $pager->setWriteRowsPerPageOptions(true, 12);
      $pager->handle();

      $categories = (new GetCategories('nl'))->getAllOnline(); // 1st layer: all top level categories
      $categoryParents = (new GetCategories($_SESSION['lang']))->getParentsOf($category);
      $subCategories = (new GetCategories($_SESSION['lang']))->getSubCategoriesOf($category->id, $pager); // 2nd layer: all subcategories

      array_walk($subCategories, function ($category) {
        $this->setImageUrl($category);
      });

      $products = [];
      if (count($subCategories) == 0) { // 3rd layer: all productcontainers (with pagination)
        $products = (new GetProductContainers())->getOnlineOfCategory($category->id, $pager);

        // if no products are found and a page number is set, redirect to the first page
        if (empty($products) && $pager->pageNum > 1) ResponseHelper::redirect(reconstructQueryAdd());
      }
      array_walk($products, function ($product) {
        $this->setImageUrl($product);
      });

      $breadcrumbs = BreadCrumbs::getInstance();
      $breadcrumbs->clearItems();
      $breadcrumbs->addItem("Home", "/");
      foreach ($categoryParents as $_category) {
        $breadcrumbs->addItem($_category->content->name, $_category->id == $category->id ? null : $_category->content->getUrl()); // no link on self
      }

      $this->seo_title = $category->getSeoTitle($_SESSION['lang']);
      $this->seo_description = $category->getSeoDesc($_SESSION['lang']);
      if ($photo = $category->getPhoto(true)) {
        $this->seo_image = $this->site->site_host->getDomain(true) . $photo;
      }

      $this->pager = $pager;
      $this->show_breadcrumbs = true;
      $this->category = $category;
      $this->products = $products;
      $this->categories = $categories;
      $this->subCategories = $subCategories;
    }

    public function executeProduct() {
      Context::addJavascriptInlineBottomFile(DIR_PROJECT_FOLDER . 'modules/site/js/parralax.js');

      if (!isset($_GET['var2']) || (int)$_GET['var2'] == 0) {
        MessageFlashCoordinator::addMessageAlert('Helaas... Dit product is niet meer beschikbaar. U bent doorgestuurd naar de homepage.');
        ResponseHelper::redirect301('/');
      }

      $productContainerId = (int)$_GET['var2'];

      $getProductContainer = new GetProductContainer($productContainerId);
      $productFound = false;
      try {
        $productContainer = $getProductContainer->getOnline();
        $this->setImageUrl($productContainer);

        $productContainer->content = $getProductContainer->getContent();
        $productContainer->options = $getProductContainer->getOptions();
        $productContainer->related = $getProductContainer->getRelated();
        array_walk($productContainer->related, function ($product) {
          $this->setImageUrl($product);
        });

        $getProducts = new GetProducts();
        $products = $getProducts->getOnline($productContainer);
        foreach ($products as $product) {
          $product->options = $getProducts->getOptions($product);
        }
        $getCategory = new GetCategory($productContainer->category1_id);
        $category = $getCategory->getOnline();
        $productFound = true;
      }
      catch (Exception $exception) {
        logToFile('executeProduct', print_r($exception, true));
      }

      if ($productFound === false) {
        MessageFlashCoordinator::addMessageAlert('Helaas... Dit product is niet meer beschikbaar. U bent doorgestuurd naar de homepage.');
        ResponseHelper::redirect301('/');
      }

      $categoryParents = new GetCategories($_SESSION['lang'])->getParentsOf($category);

      $breadcrumbs = BreadCrumbs::getInstance();
      $breadcrumbs->clearItems();
      $breadcrumbs->addItem("Home", "/");

      foreach ($categoryParents as $_category) {
        BreadCrumbs::getInstance()->addItem($_category->content->name, $_category->content->getUrl());
      }

      BreadCrumbs::getInstance()->addItem($productContainer->content->name);

      $this->show_breadcrumbs = true;
      $this->category = $category;
      $this->product_container = $productContainer;
      $this->products = $products;

      $this->seo_title = $this->product_container->content->name;
      if (!empty($this->product_container->content->material)) {
        $this->seo_title .= ' ' . $this->product_container->content->material;
      }
      $this->seo_title .= ' ';
      if (!empty($this->product_container->content->description)) {
        $this->seo_description = strip_tags($this->product_container->content->description);
      }
    }

    private function setImageUrl($object): void {
      if (property_exists($object, 'image_orig') && $object->image_orig) {
        $object->imageUrl = URL_UPLOADS . 'images/catalog/' . $object->image_orig;
      }
      elseif (property_exists($object, 'foto_orig') && $object->foto_orig) {
        $object->imageUrl = URL_UPLOADS . 'images/catalog/' . $object->foto_orig;
      }
      else {
        $object->imageUrl = $this->site->getTemplateUrl() . "images/default-thumbnail.jpg";
      }
    }
  }