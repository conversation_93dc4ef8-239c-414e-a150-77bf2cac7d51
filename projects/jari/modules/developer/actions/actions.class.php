<?php

  use domain\aboservice\service\AboserviceApi;

  class developerJariActions extends developerActions {

    public function executeScripts() {

      ini_set('memory_limit', '1056M');
      set_time_limit(3600);

//      $this->clearCategories();
//      $this->clearProducts();

//      $username = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-username');
//      $password = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-password');
//      $token = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-token');
//      $tokenDatetime = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-token-datetime');
//      if (!$token) $token = new Setting(['type' => 'ABOSERVICE', 'code' => 'aboservice-token']);
//      if (!$tokenDatetime) $tokenDatetime = new Setting(['type' => 'ABOSERVICE', 'code' => 'aboservice-token-datetime']);
//      $api = new AboserviceApi($username, $password, $token, $tokenDatetime);

//      $this->setCategories($api);
//      $this->setProducts($api); // LET OP! Dit zijn 48k+ producten

//      $this->importProductOptions();

      echo '<pre>DONE</pre>';

      $this->template = null;
    }

    /**
     * Import options with either diameter or both diameter and length set
     * @return void
     * @throws GsdDbException
     * @throws GsdException
     */
    private function importProductOptions() {
      $objReader = new PhpOffice\PhpSpreadsheet\Reader\Xlsx();
      $objReader->setReadDataOnly(true);
      $objPHPExcel = $objReader->load(DIR_UPLOADS . 'products-with-options.xlsx');

      $products = AppModel::mapObjectIds(Product::find_all(), 'code');

      $notFound = [];
      $invalidDiameter = [];
      $invalidLength = [];

      $sheetIndex = 5; // 0 through 5
      $objPHPExcel->setActiveSheetIndex($sheetIndex);
      $sheet = $objPHPExcel->getActiveSheet();
      foreach ($sheet->toArray() as $rowNr => $row) {
        if ($rowNr === 0 || empty($row[0])) continue;

        $code = preg_replace('/\./', '', $row[0], 1);
        $product = $products[$code] ?? false;
        if (!$product) {
          $notFound[] = [$code];
          continue;
        }

        $diameter = $row[7];
        if (empty($diameter) || (!is_numeric($diameter))) {
          $invalidDiameter[] = [$code];
          continue;
        }

        $diameterOption = ProductOption::getDiameterOption($product->id);
        if (!$diameterOption) {
          ProductOption::createNewDiameterOption($product->id, $diameter);
        }

        $length = $row[9];
        if (empty($length) || (!is_numeric($length))) {
          $invalidLength[] = [$code];
          continue;
        }

        $lengthOption = ProductOption::getLengthOption($product->id);
        if (!$lengthOption) {
          ProductOption::createNewLengthOption($product->id, $length);
        }
      }

      // save errors to csv
      $headerRow = [["Artikelcode"]];

      if (ArrayHelper::hasData($notFound)) {
        $notFoundRows = array_merge($headerRow, $notFound);
        $filepath = DIR_TEMP . "not-found-sheet" . ($sheetIndex + 1) . ".csv";
        FileHelper::saveArrayToCSV($notFoundRows, $filepath, ";");
      }

      if (ArrayHelper::hasData($invalidDiameter)) {
        $invalidDiameterRows = array_merge($headerRow, $invalidDiameter);
        $filepath = DIR_TEMP . "invalid-diameter-sheet" . ($sheetIndex + 1) . ".csv";
        FileHelper::saveArrayToCSV($invalidDiameterRows, $filepath, ";");
      }

      if (ArrayHelper::hasData($invalidLength)) {
        $invalidLengthRows = array_merge($headerRow, $invalidLength);
        $filepath = DIR_TEMP . "invalid-length-sheet" . ($sheetIndex + 1) . ".csv";
        FileHelper::saveArrayToCSV($invalidLengthRows, $filepath, ";");
      }
    }

    private function setProducts($api) {
      $lastId = '0081245';
      while ($lastId !== false) {
        $data = $api->getProducts($lastId);
        $products = $data['data'];

        $this->fillProductsFromApi($products);

        $lastId = $data['serverload'] ? end($products)['id'] : false;
      }
    }

    private function setCategories($api) {
      $groups = $api->getCategoryGroups();
      $categoryTree = AboserviceApi::buildCategoryTreeFromGroups($groups['data']);
      $this->fillCategoriesFromTree($categoryTree);

      dumpe($categoryTree);
    }

    private function clearCategories() {
      DBConn::db_link()->query("SET FOREIGN_KEY_CHECKS = 0;");
      DBConn::db_link()->query("TRUNCATE TABLE " . Category::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . CategoryContent::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . JariCategoryUid::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . JariProductContainerUid::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . ProductCont::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . ProductContContent::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . ProductContOption::getTablename());
      DBConn::db_link()->query("SET FOREIGN_KEY_CHECKS = 1;");

      dumpe('cleared');
    }

    private function getProductPageCount() {
      $lastId = '';
      $serverLoad = true;
      $totalPages = 0;
      $startTime = microtime(true);
      $maxDuration = 60; // seconds

      while ($serverLoad) {
        if ((microtime(true) - $startTime) > $maxDuration) {
          break;
        }

        $result = $api->getProducts($lastId);
        $lastId = end($result['data'])['id'];
        $serverLoad = $result['serverload'];
        $totalPages++;
      }

      dumpe($totalPages);
    }

    private function clearProducts() {
      DBConn::db_link()->query("SET FOREIGN_KEY_CHECKS = 0;");
      DBConn::db_link()->query("TRUNCATE TABLE " . Product::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . ProductContent::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . ProductOption::getTablename());
      DBConn::db_link()->query("TRUNCATE TABLE " . JariProductUid::getTablename());
      DBConn::db_link()->query("SET FOREIGN_KEY_CHECKS = 1;");

      dumpe('cleared');
    }

    private function fillProductsFromApi($apiData) {

      $skipped = 0;
      foreach ($apiData as $data) {
        array_walk($data, function (&$value) {
          $value = trim($value);
        }); // cleanup

        $productCont = JariProductContainerUid::find_by(['abo_uid' => $data['groepuid']]);
        if (!$productCont || empty($data['id']) || empty($data['art_nummer']) || empty($data['omschr_n'])) {
          logToFile('abo-product-sync-error', print_r($data, true));
          $skipped++;
          continue;
        }

        if (Product::find_by(['code' => $data['art_nummer']])) {
          logToFile('abo-product-sync-error', sprintf("Product code %s already found", $data['art_nummer']));
          $skipped++;
          continue;
        }

        $product = new Product([ // create product
          'product_cont_id' => $productCont->product_container_id,
          'code'            => $data['art_nummer'],
          'online_custshop' => 1,
          'size'            => $data['maat'] ?? null,
        ]);
        $product->save();

        // for now we don't generate an url, products are kept in a container
        // @todo bespreken met Robert wat slim is qua SEO
        $content = new ProductContent([ // create product content
          'product_id' => $product->id,
          'name'       => $data['omschr_n'],
        ]);
        $content->save();

        // link to ABO id for api calls
        $aboUid = new JariProductUid([
          'product_id' => $product->id,
          'abo_uid'    => $data['id'],
        ]);
        $aboUid->save();
      }

      if ($skipped > 0) {
        echo "<br><br>Er zijn producten overgeslagen. Controleer de logs.<br><br>";
      }
    }

    private function fillCategoriesFromTree($categoryTree) {
      foreach ($categoryTree as $uid => $category) { // layer 1
        try {
          $firstLayer = Category::createFromAboGroupData($category, $uid); // create category
          if (!$firstLayer) echo $uid . ' already exists.<br>';
        }
        catch (Exception $e) {
          echo $category['uid'] . ' was not created. see logs for more info.';
          logToFile('fillCategoriesFromTree', print_r($e, true));
        }

        if (ArrayHelper::hasData($category['children'])) { // layer 2
          foreach ($category['children'] as $subcategory) {
            try {
              $secondLayer = isset($firstLayer->id) ? Category::createFromAboGroupData($subcategory, $subcategory['uid'], $firstLayer->id) : null; // create subcategory
              if (!$secondLayer) echo $subcategory['uid'] . ' already exists.<br>';
            }
            catch (Exception $e) {
              echo $subcategory['uid'] . ' was not created. see logs for more info.';
              logToFile('fillCategoriesFromTree', print_r($e, true));
            }

            if (ArrayHelper::hasData($subcategory['children'])) { // layer 3
              foreach ($subcategory['children'] as $productContainer) {
                try {
                  $thirdLayer = isset($secondLayer->id) ? ProductCont::createFromAboGroupData($productContainer, $productContainer['uid'], $secondLayer->id) : null; // create productcontainer
                  if (!$thirdLayer) echo $productContainer['uid'] . ' already exists.<br>';
                }
                catch (Exception $e) {
                  echo $productContainer['uid'] . ' was not created. see logs for more info.';
                  logToFile('fillCategoriesFromTree', print_r($e, true));
                }
              }
            }
          }
        }
      }
    }

  }
