<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  container: {
    type: Object,
    default: null
  },
  isOpen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'add-to-basket']);

const dialog = ref(null);

watch(() => props.isOpen, (newValue) => {
  if (newValue && dialog.value) {
    dialog.value.showModal();
  } else if (dialog.value) {
    dialog.value.close();
  }
});

function closeDialog() {
  emit('close');
}

function handleDialogClick(event) {
  if (event.target === dialog.value) {
    closeDialog();
  }
}

function handleKeydown(event) {
  if (event.key === 'Escape') {
    closeDialog();
  }
}

function handleAddToBasket() {
  emit('add-to-basket', props.product);
  closeDialog();
}
</script>

<template>
  <dialog
      ref="dialog"
      class="product-dialog"
      @click="handleDialogClick"
      @keydown="handleKeydown"
  >
    <div class="dialog-content" v-if="product">
      <div class="dialog-header">
        <h2>Artikel toevoegen</h2>
        <button
            type="button"
            class="close-button"
            @click="closeDialog"
            aria-label="Close dialog"
        >
          ×
        </button>
      </div>

      <div class="dialog-body">
        <div class="product-info">
          <div class="product-details">
            <div class="detail-row" v-if="product.code">
              <span class="label">Artikelnummer:</span>
              <span class="value">{{ product.code }}</span>
            </div>
          </div>

          <div class="product-options" v-if="product.options && product.options.length">
            <h4>Specificaties</h4>
            <div class="options-grid">
              <div
                  v-for="option in product.options"
                  :key="option.code"
                  class="option-item"
              >
                <span class="option-label">{{ option.name }}:</span>
                <span class="option-value">{{ option.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <footer class="dialog-footer">
        <button type="button" class="btn btn-secondary" @click="closeDialog">
          Annuleren
        </button>
        <button type="button" class="btn btn-primary" @click="handleAddToBasket">
          Toevoegen
        </button>
      </footer>
    </div>
  </dialog>
</template>

<style scoped lang="scss">
.product-dialog {
  border: none;
  border-radius: 8px;
  padding: 0;
  max-width: 500px;
  width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin: auto;

  &::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e5e5;
  background-color: var(--color-primary, #007bff);
  color: white;
  border-radius: 8px 8px 0 0;

  h2 {
    margin: 0;
    font-size: 1.25rem;
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

.dialog-body {
  flex: 1;
  padding: 1.5rem;
}

.product-info {
  h3 {
    margin: 0 0 1rem 0;
    color: var(--color-primary, #007bff);
  }
}

.product-details {
  margin-bottom: 1.5rem;
}

.detail-row {
  display: flex;
  margin-bottom: 0.5rem;

  .label {
    font-weight: 600;
    min-width: 80px;
    color: #666;
    margin-right: 0.5rem;
  }

  .value {
    color: #333;
  }
}

.product-options {
  h4 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1rem;
  }
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.option-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 4px;

  .option-label {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0.25rem;
  }

  .option-value {
    font-weight: 600;
    color: #333;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e5e5;
  background-color: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;

  &.btn-secondary {
    background-color: #6c757d;
    color: white;

    &:hover {
      background-color: #5a6268;
    }
  }

  &.btn-primary {
    background-color: var(--color-primary, #007bff);
    color: white;

    &:hover {
      background-color: var(--color-primary-dark, #0056b3);
    }
  }
}

@media (max-width: 600px) {
  .product-dialog {
    width: 95vw;
    max-width: none;
  }

  .options-grid {
    grid-template-columns: 1fr;
  }

  .dialog-footer {
    flex-direction: column;

    .btn {
      width: 100%;
    }
  }
}
</style>